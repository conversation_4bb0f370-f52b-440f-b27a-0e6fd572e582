package com.project.management.notes.mappers

import com.project.management.common.annotation.NewMappingOrganizationEntity
import com.project.management.users.User
import com.project.management.notes.models.CreateNoteRequest
import com.project.management.notes.models.NoteEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface NotesMapper {

    @NewMappingOrganizationEntity
    @Mapping(target = "createdBy", source = "createdBy")
    fun toNoteEntity(
        request: CreateNoteRequest,
        organizationId: Long,
        createdBy: User,
        userId: Long?,
    ): NoteEntity
}