package com.project.management.money.project

import com.project.management.beneficiaries.mappers.toEntity
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.customers.mappers.toCustomerTransaction
import com.project.management.customers.models.CustomerTransaction
import com.project.management.customers.validators.CustomerValidator
import com.project.management.projects.mappers.ProjectIncomeMapper
import com.project.management.projects.mappers.toEntity
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.projects.repositories.ProjectExpenseQueryRepository
import com.project.management.projects.repositories.ProjectIncomeRepository
import com.project.management.projects.repositories.ProjectRepository
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.projects.validators.ProjectValidator
import org.hibernate.query.results.Builders.entity
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class ProjectExpensesIncomesMoneyMutateService(
    private val transactions: ProjectExpenseMutateRepository,
    private val transactionsRepo: ProjectExpenseQueryRepository,
    private val incomeTransactions: ProjectIncomeRepository,
    private val projectRepository: ProjectRepository,
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val customerValidator: CustomerValidator,
    private val projectIncomeMapper: ProjectIncomeMapper,
) {

    @Transactional
    fun projectExpenseAdd(
        request: PostRequestProjectExpense,
        transaction: BeneficiaryTransaction,
        projectId: Long
    ): ProjectExpenseEntity {
        increaseExpenses(
            projectId = projectId,
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal()
        )

        return createExpense(request, transaction, projectId)
    }

    @Transactional
    fun projectExpenseDelete(expense: ProjectExpense): ProjectEntity {
        deleteExpense(projectExpenseId = expense.id!!)
        return decreaseExpenses(
            projectId = expense.projectId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }

    @Transactional
    fun projectExpenseModify(
        projectId: Long,
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): ProjectEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseExpenses(
            projectId = projectId,
            amount = amount,
            amountPaid = amountPaid,
        )
    }

    @Transactional
    fun projectExpenseChange(expense: ProjectExpense, oldProjectId: Long, newProjectId: Long) {
        decreaseExpenses(
            projectId = oldProjectId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
        increaseExpenses(
            projectId = newProjectId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
        expense.projectId = newProjectId
        transactionsRepo.save(expense)
    }

    @Transactional
    fun projectExpenseChangeBeneficiary(
        expense: ProjectExpense,
        newBeneficiaryId: Long?
    ) {
        val entity = expense
        entity.beneficiaryId = newBeneficiaryId
        transactions.save(entity.toEntity())
    }

    @Transactional
    fun projectIncomeAdd(
        request: PostRequestProjectIncome,
        transaction: CustomerTransaction,
        projectId: Long
    ): ProjectIncomeEntity {
        increaseIncomes(
            projectId = projectId,
            amount = request.amount.toBigDecimal(),
            amountPaid = request.amountPaid.toBigDecimal()
        )

        return createIncome(request, transaction, projectId)
    }

    @Transactional
    fun projectIncomeDelete(income: ProjectIncomeEntity): ProjectEntity {
        deleteIncome(projectIncomeId = income.id!!)
        return decreaseIncomes(
            projectId = income.projectId,
            amount = income.customerTransaction.amount,
            amountPaid = income.customerTransaction.amountPaid
        )
    }

    @Transactional
    fun projectIncomeModify(
        projectId: Long,
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): ProjectEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseIncomes(
            projectId = projectId,
            amount = amount,
            amountPaid = amountPaid,
        )
    }

    private fun increaseExpenses(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): ProjectEntity {
        val currentUser = currentUser.getCurrentUser()
        var project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = currentUser.organizationId
        )
        val editedProject = project.copy(
            totalExpenses = project.totalExpenses.plus(amount),
            totalPaidExpenses = project.totalPaidExpenses.plus(amountPaid),
            updatedBy = currentUser.id
        )

        project = projectRepository.save(editedProject)

        return project
    }

    private fun decreaseExpenses(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): ProjectEntity {
        val currentUser = currentUser.getCurrentUser()
        var project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = currentUser.organizationId
        )
        val editedProject = project.copy(
            totalExpenses = project.totalExpenses.minus(amount),
            totalPaidExpenses = project.totalPaidExpenses.minus(amountPaid),
            updatedBy = currentUser.id
        )

        project = projectRepository.save(editedProject)

        return project
    }

    private fun createExpense(
        request: PostRequestProjectExpense,
        transaction: BeneficiaryTransaction,
        projectId: Long
    ): ProjectExpenseEntity {
        val currentUser = currentUser.getCurrentUser()
        val entity = request
            .toEntity(
                projectId,
                beneficiaryTransaction = transaction,
                user = currentUser,
                version = 1
            )

        return transactions.save(entity)
    }

    private fun deleteExpense(
        projectExpenseId: Long
    ) {
        transactions.deleteByIdAndOrganizationId(
            id = projectExpenseId,
            organizationId = currentUser.getCurrentUser().organizationId,
            updatedBy = currentUser.getCurrentUser().id!!
        )
    }

    private fun increaseIncomes(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): ProjectEntity {
        val currentUser = currentUser.getCurrentUser()
        var project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = currentUser.organizationId
        )
        val editedProject = project.copy(
            totalIncomes = project.totalIncomes.plus(amount),
            totalPaidIncomes = project.totalPaidIncomes.plus(amountPaid),
            updatedBy = currentUser.id
        )

        project = projectRepository.save(editedProject)

        return project
    }

    private fun decreaseIncomes(
        projectId: Long, amount: BigDecimal, amountPaid: BigDecimal
    ): ProjectEntity {
        val currentUser = currentUser.getCurrentUser()
        var project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = currentUser.organizationId
        )
        val editedProject = project.copy(
            totalIncomes = project.totalIncomes.minus(amount),
            totalPaidIncomes = project.totalPaidIncomes.minus(amountPaid),
            updatedBy = currentUser.id
        )

        project = projectRepository.save(editedProject)

        return project
    }

    private fun createIncome(
        request: PostRequestProjectIncome,
        transaction: CustomerTransaction,
        projectId: Long
    ): ProjectIncomeEntity {
        val currentUser = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = request.customerId,
            organizationId = currentUser.organizationId
        )
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = currentUser.organizationId
        )

        val entity = projectIncomeMapper.toProjectIncome(
            projectIncomeRequestDto = request,
            customerTransactionId = transaction.id!!,
            customer = customer,
            organizationId = currentUser.organizationId,
            projectId = projectId,
            userId = currentUser.id,
            customerTransaction = transaction,
            project = project,
            version = 1
        )

        return incomeTransactions.save(entity)
    }

    private fun deleteIncome(
        projectIncomeId: Long
    ) {
        incomeTransactions.deleteByIdAndOrganizationId(
            id = projectIncomeId,
            organizationId = currentUser.getCurrentUser().organizationId,
            updatedBy = currentUser.getCurrentUser().id!!
        )
    }
}
