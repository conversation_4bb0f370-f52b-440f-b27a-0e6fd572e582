package com.project.management.money.project.usecases

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.common.exceptions.BusinessException
import com.project.management.money.user.UserMoneyMutateService
import com.project.management.money.beneficiary.BeneficiaryMoneyMutateService
import com.project.management.money.organization.OrganizationProjectsMoneyMutateService
import com.project.management.money.project.ProjectExpensesIncomesMoneyMutateService
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import com.project.management.users.models.User
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class CreateProjectExpenseUseCase(
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiary: BeneficiaryMoneyMutateService,
    private val projectMoneyMutateService: ProjectExpensesIncomesMoneyMutateService,
    private val userMoneyMutateService: UserMoneyMutateService,
    private val organizationProjectsMoneyMutateService: OrganizationProjectsMoneyMutateService
) {

    @Transactional
    fun create(request: PostRequestProjectExpense, projectId: Long): ProjectExpense {
        val user = currentUser.getCurrentUser()
        validate(request, user, projectId)

        val transaction = beneficiaryScenario(request, projectId)
        val entity = projectScenarios(request, projectId, transaction)
        val expense = projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = entity.id!!,
            organizationId = user.organizationId
        )

        userScenario(user = user, expense = expense)
        organizationScenario(expense = expense)

        return expense
    }


    private fun userScenario(user: User, expense: ProjectExpense) {
        userMoneyMutateService.projectExpenseAdd(user.id!!, expense)
    }

    private fun beneficiaryScenario(
        request: PostRequestProjectExpense,
        projectId: Long
    ): BeneficiaryTransaction {
        return beneficiary.projectExpenseAdd(request = request, projectId = projectId)
    }

    private fun organizationScenario(expense: ProjectExpense) {
        organizationProjectsMoneyMutateService.increaseExpenses(
            amount = expense.beneficiaryTransaction.amountPaid,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }


    private fun projectScenarios(
        request: PostRequestProjectExpense,
        projectId: Long,
        transaction: BeneficiaryTransaction
    ): ProjectExpenseEntity {
        return projectMoneyMutateService.projectExpenseAdd(
            request = request,
            projectId = projectId,
            transaction = transaction
        )
    }

    private fun validate(request: PostRequestProjectExpense, user: User, project: Long) {
        // Validate project (required)
        projectValidator.validateExistsByIdAndOrganizationId(
            projectId = project,
            organizationId = user.organizationId
        )

        validateBeneficiary(request, user)

        // Validate terms consistency - both must be provided together or both must be null
        if (request.termsGroupId != null && request.termId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together or both must be null.")
        }

        if (request.termId != null && request.termsGroupId == null) {
            throw BusinessException.BadRequestException(message = "Both termsGroupId and termId must be provided together or both must be null.")
        }

        // Early return if no terms provided
        if (request.termsGroupId == null && request.termId == null) {
            return
        }

        // Validate terms when both are provided
        val termsGroup = termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
            termsGroupId = request.termsGroupId!!,
            organizationId = user.organizationId
        )
        val term = termValidator.validateTermExistsByIdAndOrganizationId(
            termId = request.termId!!,
            organizationId = user.organizationId
        )

        if (term.termsGroupId != termsGroup.id) {
            throw BusinessException.ConflictException(message = "Term and TermsGroup do not match.")
        }
    }

    private fun validateBeneficiary(request: PostRequestProjectExpense, user: User) {
        // Early return if no beneficiary provided
        if (request.beneficiaryId == null) return
        BeneficiaryRepository.Query
            .getById(OrganizationId(user.organizationId), request.beneficiaryId).validate()
    }
}
