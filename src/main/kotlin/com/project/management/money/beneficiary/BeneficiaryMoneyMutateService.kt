package com.project.management.money.beneficiary

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.beneficiaries.mappers.toBeneficiaryTransaction
import com.project.management.beneficiaries.mappers.toEntity
import com.project.management.beneficiaries.Beneficiary
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.beneficiaries.repositories.BeneficiaryTransactionMutateRepository
import com.project.management.beneficiaries.validators.BeneficiaryTransactionValidator
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import com.project.management.projects.requests.PostRequestProjectExpense
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class BeneficiaryMoneyMutateService(
    private val transactions: BeneficiaryTransactionMutateRepository,
    private val transactionsValidator: BeneficiaryTransactionValidator,
    private val currentUser: CurrentUserConfig,
) {

    @Transactional
    fun projectExpenseAdd(
        request: PostRequestProjectExpense,
        projectId: Long
    ): BeneficiaryTransaction {
        // Only increase accumulators if beneficiary is provided
        if (request.beneficiaryId != null) {
            increaseAccumulators(
                beneficiaryId = request.beneficiaryId,
                amount = request.amount.toBigDecimal(),
                amountPaid = request.amountPaid.toBigDecimal()
            )
        }

        return createTransaction(request, projectId)
    }

    @Transactional
    fun projectExpenseDelete(
        beneficiaryId: Long?,
        transactionId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Beneficiary? {
        deleteTransaction(transactionId = transactionId)

        // Only decrease accumulators if beneficiary is provided
        if (beneficiaryId == null) return null

        return decreaseAccumulators(
            beneficiaryId = beneficiaryId,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    @Transactional
    fun projectExpenseModify(
        transaction: BeneficiaryTransaction,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): Beneficiary? {
        val amount = transaction.amount.negate() + newAmount
        val amountPaid = transaction.amountPaid.negate() + newAmountPaid

        updateTransaction(transaction = transaction, amount = newAmount, amountPaid = newAmountPaid)

        // Only update accumulators if beneficiary is provided
        if (transaction.beneficiaryId == null) return null

        return increaseAccumulators(
            beneficiaryId = transaction.beneficiaryId!!,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    @Transactional
    fun projectExpenseChangeProject(transaction: BeneficiaryTransaction, newProjectId: Long) {
        val entity = transaction
        entity.projectId = newProjectId
        transactions.save(entity)
    }

    @Transactional
    fun projectExpenseChangeBeneficiary(
        transactionId: Long,
        oldBeneficiaryId: Long?,
        newBeneficiaryId: Long?
    ) {
        val transaction = transactionsValidator.findByIdAndOrganizationId(
            id = transactionId,
            organizationId = currentUser.getCurrentUser().organizationId
        )
        if (oldBeneficiaryId == newBeneficiaryId) return

        // Only update accumulators if beneficiary is provided
        if (oldBeneficiaryId != null) {
            decreaseAccumulators(
                beneficiaryId = oldBeneficiaryId,
                amount = transaction.amount,
                amountPaid = transaction.amountPaid
            )
        }
        if (newBeneficiaryId != null) {
            increaseAccumulators(
                beneficiaryId = newBeneficiaryId,
                amount = transaction.amount,
                amountPaid = transaction.amountPaid
            )
        }
        val entity = transaction
        entity.beneficiaryId = newBeneficiaryId
        transactions.save(entity)
    }

    private fun increaseAccumulators(
        beneficiaryId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Beneficiary {
        val currentUser = currentUser.getCurrentUser()
        var beneficiary = BeneficiaryRepository
            .Query.getById(OrganizationId(currentUser.organizationId), beneficiaryId).validate()

        beneficiary.balanceAccumulator = beneficiary.balanceAccumulator.plus(amount)
        beneficiary.paidAccumulator = beneficiary.paidAccumulator.plus(amountPaid)
        beneficiary.updatedBy = currentUser.id!!

        return BeneficiaryRepository.Mutate.save(beneficiary)
    }

    private fun decreaseAccumulators(
        beneficiaryId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): Beneficiary {
        val currentUser = currentUser.getCurrentUser()
        var beneficiary = BeneficiaryRepository
            .Query.getById(OrganizationId(currentUser.organizationId), beneficiaryId).validate()

        beneficiary.balanceAccumulator = beneficiary.balanceAccumulator.minus(amount)
        beneficiary.paidAccumulator = beneficiary.paidAccumulator.minus(amountPaid)
        beneficiary.updatedBy = currentUser.id!!

        return BeneficiaryRepository.Mutate.save(beneficiary)
    }

    private fun createTransaction(
        request: PostRequestProjectExpense,
        projectId: Long
    ): BeneficiaryTransaction {
        val currentUser = currentUser.getCurrentUser()
        val entity = request
            .toBeneficiaryTransaction(projectId)
            .toEntity(request.beneficiaryId, currentUser, 1)

        return transactions.save(entity)
    }

    private fun updateTransaction(
        transaction: BeneficiaryTransaction,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): BeneficiaryTransaction {
        val currentUser = currentUser.getCurrentUser()

        val updatedTransaction = transaction

        updatedTransaction.amount = amount
        updatedTransaction.amountPaid = amountPaid
        updatedTransaction.updatedBy = currentUser.id!!


        return transactions.save(updatedTransaction)
    }

    private fun deleteTransaction(transactionId: Long) {
        val performer = currentUser.getCurrentUser()
        transactions.deleteByIdAndOrganizationId(
            id = transactionId,
            organizationId = performer.organizationId,
            updatedBy = performer.id!!
        )
    }
}

