package com.project.management.auth.services

import com.project.management.common.utility.JwtUtility
import com.project.management.organization.requests.OrganizationRequestDto
import com.project.management.auth.requests.RegisterRequestDto
import com.project.management.users.requests.UserRequestDto
import com.project.management.auth.responses.LoginResponseDto
import com.project.management.auth.responses.RegisterResponseDto
import com.project.management.organization.models.OrganizationEntity
import com.project.management.users.User
import com.project.management.organization.mappers.OrganizationMapper
import com.project.management.users.mappers.UserMapper
import com.project.management.organization.repositories.OrganizationRepository
import com.project.management.users.repositories.UserRepository
import com.project.management.auth.validators.AuthValidator
import com.project.management.organization.mappers.toEntity
import com.project.management.users.mappers.toEntity
import com.project.management.users.validators.UserValidator
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class AuthService(
    private val userRepository: UserRepository,
    private val organizationRepository: OrganizationRepository,
    private val userMapper: UserMapper,
    private val organizationMapper: OrganizationMapper,
    private val userValidator: UserValidator,
    private val authValidator: AuthValidator,
    private val passwordEncoder: PasswordEncoder,
    private val jwtUtility: JwtUtility,
) {
    @Transactional
    fun registerUser(registerDto: RegisterRequestDto): RegisterResponseDto {
        var user = createUser(registerDto.user)
        var organization = createOrganization(registerDto.organization, user.organizationCode, user)
        user.organizationId = organization.id!!
        user.organizationCode = organization.id!!.toString()
        user = userRepository.save(user)

        organization.organizationCode = organization.id!!.toString()
        organization = organizationRepository.save(organization)

        return RegisterResponseDto(
            user = userMapper.toUserResponseDto(user),
            organization = organizationMapper.toOrganizationResponseDto(
                organization,
                expenses = 0.0.toBigDecimal(),
                incomes = 0.0.toBigDecimal(),
                totalCapital = 0.0.toBigDecimal(),
                paidIncomes = 0.0.toBigDecimal()
            ),
        )
    }

    fun loginUser(username: String, password: String, organizationCode: String): LoginResponseDto {
        val user = userValidator.validateUserExistsByUsernameAndOrganizationCode(
            username,
            organizationCode
        )
        authValidator.validPassword(password, user.password)
        return LoginResponseDto(
            organizationCode = user.organizationCode,
            token = jwtUtility.generateToken(user.username, user.organizationCode),
            refreshToken = jwtUtility.generateRefreshToken(user.username, user.organizationCode),
            userId = user.id!!
        )
    }

    fun refreshToken(refreshToken: String): LoginResponseDto {
        authValidator.isTokenValid(refreshToken)
        val claims = jwtUtility.extractAllClaims(refreshToken)
        val username = claims.subject
        val organizationCode = claims["organization_code"] as String
        val user = userValidator.validateUserExistsByUsernameAndOrganizationCode(
            username,
            organizationCode
        )
        return LoginResponseDto(
            organizationCode = username,
            token = jwtUtility.generateToken(username, organizationCode),
            refreshToken = jwtUtility.generateRefreshToken(username, organizationCode),
            userId = user.id!!
        )
    }

    private fun createOrganization(
        organizationDto: OrganizationRequestDto,
        organizationCode: String,
        user: User
    ): OrganizationEntity {
        val organization = organizationDto.toEntity(user)
        organization.organizationCode = organizationCode
        organizationRepository.save(organization)
        return organization
    }

    private fun createUser(userDto: UserRequestDto): User {
        authValidator.isPasswordMatch(userDto.password, userDto.confirmPassword)
        val user = userDto.toEntity(
            organizationId = -1,
            createdBy = null,
            updatedBy = null,
            organizationCode = "",
            isAdmin = true
        )
        user.password = passwordEncoder.encode(user.password)
        userRepository.save(user)
        return user
    }
}