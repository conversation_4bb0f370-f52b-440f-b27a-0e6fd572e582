package com.project.management.common.controller

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.users.User
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.security.Principal

@RestController
@RequestMapping("/api/v1/protected")
class ProtectedController(
    private val currentUser: CurrentUserConfig
) {

    @GetMapping
    fun getProtected(principal: Principal): User {
        val user = currentUser.getCurrentUser()
        return user
    }
    
}