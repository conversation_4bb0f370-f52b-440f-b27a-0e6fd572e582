package com.project.management.common.utility

import com.project.management.common.configuration.CurrentUserConfig
import jakarta.persistence.EntityManager

fun <T> filterSoftDelete(block: () -> T): T {
    val entityManager: EntityManager by autowired()
    val session = entityManager.unwrap(org.hibernate.Session::class.java)

    session.enableFilter(Constants.softDeleteFilter)
    val result = block()
    session.disableFilter(Constants.softDeleteFilter)

    return result
}