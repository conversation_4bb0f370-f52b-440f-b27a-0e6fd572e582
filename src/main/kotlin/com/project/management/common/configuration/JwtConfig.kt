package com.project.management.common.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import com.project.management.common.exceptions.ErrorResponse
import com.project.management.common.utility.JwtUtility
import com.project.management.users.User
import com.project.management.users.validators.UserValidator
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.http.HttpStatus
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter

@Component
class JwtConfig(
    private val jwtUtil: JwtUtility,
    private val userValidator: UserValidator,
    private val objectMapper: ObjectMapper,
) : OncePerRequestFilter() {

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain,
    ) {
        val authorizationHeader = request.getHeader("Authorization")

        var jwt: String? = null
        var username: String? = null
        var organizationCode: String? = null

        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            jwt = authorizationHeader.substring(7)
            val claims = try {
                jwtUtil.extractAllClaims(jwt)
            } catch (e: Exception) {
                handleJwtException(response, e)
                return
            }

            username = claims.subject
            organizationCode = claims["organization_code"] as String
        }

        if (jwt != null && username != null && organizationCode != null && SecurityContextHolder.getContext().authentication == null) {
            val user: User = userValidator.validateUserExistsByUsernameAndOrganizationCodeAndNotDeleted(username, organizationCode)

            if (jwtUtil.validateToken(jwt, user.username)) {
                val authenticationToken = UsernamePasswordAuthenticationToken(
                    user, null, null
                )
                authenticationToken.details = WebAuthenticationDetailsSource().buildDetails(request)
                SecurityContextHolder.getContext().authentication = authenticationToken
            }
        }

        filterChain.doFilter(request, response)
    }

    private fun handleJwtException(response: HttpServletResponse, e: Exception) {
        response.status = HttpStatus.UNAUTHORIZED.value()
        response.setHeader(
            "WWW-Authenticate",
            "Bearer, error=\"expired_token\", error_description=\"The access token expired\""
        )
        response.contentType = "application/json"
        val errorResponse = ErrorResponse(HttpStatus.UNAUTHORIZED.value(), e.message ?: "Unauthorized")
        response.writer.write(objectMapper.writeValueAsString(errorResponse))
    }
}
