package com.project.management.beneficiaries.models

import com.project.management.users.models.User
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import org.hibernate.annotations.SQLRestriction
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime

@SQLRestriction("deleted IS NULL")
@Entity
@Table(name = "beneficiary_transactions")
class BeneficiaryTransaction(
    val organizationId: Long,

    var amount: BigDecimal,
    var amountPaid: BigDecimal,
    var description: String,
    var projectId: Long,
    var beneficiaryId: Long?,
    var transactionDate: ZonedDateTime,

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by", updatable = false, insertable = false)
    val createdByDetails: User,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @Column(name = "created_by")
    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
    var version: Long
) {
    @PreUpdate
    fun setLastUpdate() {
        version += 1
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}

fun BeneficiaryTransaction.toEntity(): BeneficiaryTransaction {
    return this
}