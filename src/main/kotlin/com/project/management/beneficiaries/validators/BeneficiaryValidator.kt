package com.project.management.beneficiaries.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.beneficiaries.repositories.BeneficiaryTransactionQueryRepository
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class BeneficiaryTransactionValidator(
    private val query: BeneficiaryTransactionQueryRepository
) {

    fun findAllByOrganizationId(organizationId: Long): List<BeneficiaryTransaction> {
        return query.findAllByOrganizationId(organizationId)
    }

    fun findAllByOrganizationIdAndBeneficiaryId(
        organizationId: Long,
        beneficiaryId: Long
    ): List<BeneficiaryTransaction> {
        return query.findAllByOrganizationIdAndBeneficiaryId(organizationId, beneficiaryId)
    }

    fun validateExistsByIdAndOrganization(
        beneficiaryTransactionId: Long,
        organizationId: Long
    ): BeneficiaryTransaction {
        return query.findByIdAndOrganizationId(beneficiaryTransactionId, organizationId)
            ?: throw BusinessException.NotFoundException("Beneficiary transaction with id $beneficiaryTransactionId not found")
    }

    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal {
        return query.sumAmountByOrganizationId(organizationId)
    }

    fun sumAmountPaidByOrganizationId(organizationId: Long): BigDecimal {
        return query.sumAmountPaidByOrganizationId(organizationId)
    }

    fun sumGeneralAmountByOrganizationId(organizationId: Long): BigDecimal {
        return query.sumGeneralAmountByOrganizationId(organizationId)
    }

    fun sumGeneralAmountPaidByOrganizationId(organizationId: Long): BigDecimal {
        return query.sumGeneralAmountPaidByOrganizationId(organizationId)
    }

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): BeneficiaryTransaction {
        return query.findByIdAndOrganizationId(id, organizationId)
            ?: throw BusinessException.NotFoundException("Beneficiary transaction with id $id not found")
    }
}