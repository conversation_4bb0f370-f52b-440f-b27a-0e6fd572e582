package com.project.management.beneficiaries.requests

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.common.entity.OrganizationId
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.Injection
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.models.User
import org.springframework.beans.factory.annotation.Autowired

data class PostRequestBeneficiaryTransaction(
    val amount: Double,
    val amountPaid: Double = amount,
    val description: String,
    val projectId: Long,
    val transactionDate: String
) {
    fun validate(beneficiaryId: Long?, user: User) {
        // Validate project (required)
        Injection.projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )

        // Validate amount constraints
        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }

        // Early return if no beneficiary provided
        if (beneficiaryId == null) return

        // Validate beneficiary if provided
        BeneficiaryRepository.Query.getById(
            id = beneficiaryId,
            organizationId = OrganizationId(user.organizationId)
        )
    }
}