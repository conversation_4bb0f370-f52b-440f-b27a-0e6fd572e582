package com.project.management.beneficiaries

import com.project.management.beneficiaries.Beneficiary
import com.project.management.common.entity.OrganizationId
import com.project.management.users.User

fun BeneficiaryPostRequest.toModel(performer: User): Beneficiary {
    return Beneficiary(
        name = name,
        phoneNumber = phoneNumber ?: phone_number!!,
        secondaryPhoneNumber = secondaryPhoneNumber ?: secondary_phone_number!!,
        balanceAccumulator = 0.0.toBigDecimal(),
        paidAccumulator = 0.0.toBigDecimal(),
        organizationId = performer.organizationId,
        createdBy = performer.id!!,
        updatedBy = performer.id!!,
    )
}