package com.project.management.beneficiaries

import com.project.management.beneficiaries.models.Beneficiary
import com.project.management.common.entity.OrganizationId
import com.project.management.common.utility.autowired
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

object BeneficiaryRepository {
    private val repo: Repo by autowired()

    object Query {
        fun getAll(organizationId: OrganizationId): List<Beneficiary> {
            return repo.findAllByOrganizationId(organizationId.value)
        }

        fun getById(organizationId: OrganizationId, id: Long): Beneficiary? {
            return repo.findByIdAndOrganizationId(id, organizationId.value)
        }

        fun sumAllBalance(organizationId: OrganizationId): BigDecimal {
            return repo.sumBalanceAccumulatorByOrganizationId(organizationId.value)
        }

        fun sumAllPaid(organizationId: OrganizationId): BigDecimal {
            return repo.sumPaidAccumulatorByOrganizationId(organizationId.value)
        }
    }

    object Mutate {
        fun save(beneficiary: Beneficiary): Beneficiary {
            return repo.save(beneficiary)
        }
    }
}

@Repository
private interface Repo : JpaRepository<Beneficiary, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<Beneficiary>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): Beneficiary?

    // SUM queries for beneficiary accumulators
    @Query(
        value = "SELECT COALESCE(SUM(balance_accumulator), 0) FROM beneficiaries WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumBalanceAccumulatorByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(paid_accumulator), 0) FROM beneficiaries WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumPaidAccumulatorByOrganizationId(organizationId: Long): BigDecimal
}