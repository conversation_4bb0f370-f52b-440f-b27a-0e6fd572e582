package com.project.management.beneficiaries.services

import com.project.management.beneficiaries.BeneficiaryRepository
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.beneficiaries.requests.PatchRequestBeneficiaryTransaction
import com.project.management.beneficiaries.models.toEntity
import com.project.management.beneficiaries.repositories.BeneficiaryTransactionMutateRepository
import com.project.management.beneficiaries.validators.BeneficiaryTransactionValidator
import com.project.management.common.entity.OrganizationId
import com.project.management.common.entity.validate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.ZonedDateTime

@Service
class BeneficiaryTransactionQueryService(
    private val currentUser: CurrentUserConfig,
    private val validator: BeneficiaryTransactionValidator
) {

    fun getAll(): List<BeneficiaryTransaction> {
        val user = currentUser.getCurrentUser()
        val beneficiaryTransactions = validator.findAllByOrganizationId(user.organizationId)
        return beneficiaryTransactions
    }

    fun getAllByBeneficiaryId(beneficiaryId: Long): List<BeneficiaryTransaction> {
        val user = currentUser.getCurrentUser()
        val beneficiary = BeneficiaryRepository
            .Query.getById(OrganizationId(user.organizationId), beneficiaryId).validate()

        val beneficiaryTransactions = validator.findAllByOrganizationIdAndBeneficiaryId(
            organizationId = user.organizationId,
            beneficiaryId = beneficiary.id
        )
        return beneficiaryTransactions
    }
}

@Service
class BeneficiaryTransactionMutateService(
    private val beneficiaryTransactionsRepository: BeneficiaryTransactionMutateRepository,
    private val currentUser: CurrentUserConfig,
) {
    @Transactional
    fun updateTransaction(
        request: PatchRequestBeneficiaryTransaction,
        beneficiaryTransactionId: Long
    ): BeneficiaryTransaction {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = request.validate(beneficiaryTransactionId, loggedInUser).toEntity()

        val updatedTransaction = transaction

        updatedTransaction.description = request.description ?: transaction.description
        updatedTransaction.transactionDate = if (request.transactionDate != null) {
            ZonedDateTime.parse(request.transactionDate)
        } else {
            transaction.transactionDate
        }
        updatedTransaction.updatedBy = loggedInUser.id!!


        return beneficiaryTransactionsRepository.save(updatedTransaction)
    }
}