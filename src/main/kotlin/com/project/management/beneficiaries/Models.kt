package com.project.management.beneficiaries

import com.project.management.common.entity.ServerEntity
import com.project.management.common.utility.Constants
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.math.BigDecimal

@Entity
@Table(name = "beneficiaries")
@FilterDef(name = Constants.softDeleteFilter, parameters = [])
@Filter(name = Constants.softDeleteFilter, condition = "deleted is NULL")
class Beneficiary(
    var name: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,

    var balanceAccumulator: BigDecimal,
    var paidAccumulator: BigDecimal,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
) : ServerEntity()