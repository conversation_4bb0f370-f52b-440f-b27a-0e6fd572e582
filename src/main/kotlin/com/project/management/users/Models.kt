package com.project.management.users

import com.fasterxml.jackson.annotation.JsonIgnore
import com.project.management.common.entity.ServerEntity
import com.project.management.common.utility.Constants
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.Filter
import org.hibernate.annotations.FilterDef
import java.math.BigDecimal

@Entity
@Table(name = "users")
@FilterDef(name = Constants.softDeleteFilter, parameters = [])
@Filter(name = Constants.softDeleteFilter, condition = "deleted is NULL")
class User(
    var username: String,
    @JsonIgnore
    var password: String,
    var organizationCode: String,
    var name: String,
    var email: String,
    var phoneNumber: String,
    var secondaryPhoneNumber: String?,
    var nationalId: String,
    var isAdmin: Boolean,
    var jobTitle: String,
    var balance: BigDecimal,
    var photoUrl: String,

    override val organizationId: Long,
    override val createdBy: Long,
    override var updatedBy: Long,
) : ServerEntity()
