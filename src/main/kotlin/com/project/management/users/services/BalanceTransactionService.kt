package com.project.management.users.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.notes.models.CreateNoteRequest
import com.project.management.notes.models.NoteEntity
import com.project.management.notes.models.PatchNoteRequest
import com.project.management.users.repositories.BalanceTransactionRepository
import com.project.management.notes.validators.NotesValidator
import com.project.management.users.models.UserTransaction
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class BalanceTransactionService(
    private val repository: BalanceTransactionRepository,
    private val currentUser: CurrentUserConfig
) {

    fun getAll(): List<UserTransaction> {
        val user = currentUser.getCurrentUser()
        return repository.findAllByOrganizationId(user.organizationId)
    }

    fun getByUserId(userId: Long): List<UserTransaction> {
        val user = currentUser.getCurrentUser()
        return repository.findAllByUserIdAndOrganizationId(
            userId = userId,
            organizationId = user.organizationId
        )
    }

}