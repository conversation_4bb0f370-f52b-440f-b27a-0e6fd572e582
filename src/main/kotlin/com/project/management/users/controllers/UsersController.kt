package com.project.management.users.controllers

import com.project.management.money.MoneyService
import com.project.management.users.requests.UserRequestDto
import com.project.management.users.responses.UserResponseDto
import com.project.management.users.User
import com.project.management.projects.models.UserProjectAccessEntity
import com.project.management.users.services.UsersService
import com.project.management.users.models.UserTransaction
import com.project.management.users.services.BalanceTransactionService
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1/users")
class UserController(
    private val usersService: UsersService,
    private val balanceTransactions: BalanceTransactionService,
    private val moneyService: MoneyService
) {
    @GetMapping
    fun getAll(): ResponseEntity<List<User>> {
        return ResponseEntity.ok(usersService.getAll())
    }

    @GetMapping("/projects/{projectId}/access")
    fun getAllProjectAccess(
        @PathVariable projectId: Long
    ): ResponseEntity<List<UserProjectAccessEntity>> {
        return ResponseEntity.ok(usersService.getAllProjectAccess(projectId))
    }

    @GetMapping("/{userId}/projects/{projectId}/access")
    fun getProjectAccess(
        @PathVariable userId: Long,
        @PathVariable projectId: Long
    ): ResponseEntity<UserProjectAccessEntity> {
        return ResponseEntity.ok(
            usersService.getProjectAccessByUserIdAndProjectId(
                userId,
                projectId
            )
        )
    }

    @PostMapping
    fun create(@RequestBody userRequestDto: UserRequestDto): ResponseEntity<User> {
        return ResponseEntity.ok(usersService.create(userRequestDto))
    }

    @GetMapping("/{userId}")
    fun getUser(@PathVariable userId: Long): ResponseEntity<UserResponseDto> {
        return ResponseEntity.ok(usersService.getUser(userId))
    }

    @PostMapping("/{userId}/balance/increase")
    fun increaseBalance(
        @PathVariable userId: Long,
        @RequestBody body: Map<String, Any>
    ): ResponseEntity<User> {
        return ResponseEntity.ok(moneyService.userAddBalance(userId, body["amount"] as Double))
    }

    @PostMapping("/{userId}/balance/decrease")
    fun decreaseBalance(
        @PathVariable userId: Long,
        @RequestBody body: Map<String, Any>
    ): ResponseEntity<User> {
        return ResponseEntity.ok(moneyService.userDecreaseBalance(userId, body["amount"] as Double))
    }

    @GetMapping("/{userId}/balance/transactions")
    fun getBalanceTransactions(
        @PathVariable userId: Long
    ): ResponseEntity<List<UserTransaction>> {
        return ResponseEntity.ok(balanceTransactions.getByUserId(userId))
    }

    @DeleteMapping("/{userId}")
    fun delete(@PathVariable userId: Long): ResponseEntity<Unit> {
        usersService.delete(userId)
        return ResponseEntity.status(204).build()
    }
}