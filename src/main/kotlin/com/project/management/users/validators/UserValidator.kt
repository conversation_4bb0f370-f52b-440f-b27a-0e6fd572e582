package com.project.management.users.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.Constants
import com.project.management.users.User
import com.project.management.users.repositories.UserRepository
import jakarta.persistence.EntityManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class UserValidator(
    private val userRepository: UserRepository
) {
    @Autowired
    private lateinit var entityManager: EntityManager

    fun findAll(organizationId: Long): List<User> {
        val session = entityManager.unwrap(org.hibernate.Session::class.java)
        session.enableFilter(Constants.softDeleteFilter)
        val result = userRepository.findAllByOrganizationId(organizationId)
        session.disableFilter(Constants.softDeleteFilter)

        return result
    }

    fun validateUserExistsByUsernameAndOrganizationCodeAndNotDeleted(username: String, organizationCode: String): User {
        val session = entityManager.unwrap(org.hibernate.Session::class.java)
        session.enableFilter(Constants.softDeleteFilter)
        val result = userRepository.findByUsernameAndOrganizationCodeAndDeleted(username, organizationCode, null)
        session.disableFilter(Constants.softDeleteFilter)

        return result ?: throw BusinessException.NotFoundException(message = "User does not exist.")
    }

    fun validateUserExistsByUsernameAndOrganizationCode(username: String, organizationCode: String): User {
        val session = entityManager.unwrap(org.hibernate.Session::class.java)
        session.enableFilter(Constants.softDeleteFilter)
        val result = userRepository.findByUsernameAndOrganizationCode(username, organizationCode)
        session.disableFilter(Constants.softDeleteFilter)

        return result ?: throw BusinessException.NotFoundException(message = "User does not exist.")
    }

    fun validateUserExistsByIdAndOrganizationId(userId: Long, organizationId: Long): User {
        val session = entityManager.unwrap(org.hibernate.Session::class.java)
        session.enableFilter(Constants.softDeleteFilter)
        val result = userRepository.findByIdAndOrganizationId(userId, organizationId)
        session.disableFilter(Constants.softDeleteFilter)
        return result ?: throw BusinessException.NotFoundException(message = "User does not exist.")
    }

    fun sumBalanceByOrganizationId(organizationId: Long): BigDecimal {
        return userRepository.sumBalanceByOrganizationId(organizationId)
    }
}