package com.project.management.users.repositories

import com.project.management.users.User
import org.hibernate.annotations.SQLRestriction
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface UserRepository : JpaRepository<User, Long> {
    fun findByUsername(username: String): User?
    fun findByUsernameAndOrganizationCodeAndDeleted(username: String, organizationCode: String, deleted: Unit?): User?
    fun findByUsernameAndOrganizationCode(username: String, organizationCode: String): User?
    fun findAllByOrganizationId(organizationId: Long): List<User>
    fun findByIdAndOrganizationId(id: Long, organizationId: Long): User?

    @Modifying
    @Query(
        value = "UPDATE users SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(balance), 0) FROM users WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumBalanceByOrganizationId(organizationId: Long): BigDecimal
}