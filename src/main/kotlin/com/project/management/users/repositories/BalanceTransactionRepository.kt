package com.project.management.users.repositories

import com.project.management.notes.models.NoteEntity
import com.project.management.users.models.UserTransaction
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal

@Repository
interface BalanceTransactionRepository : JpaRepository<UserTransaction, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<UserTransaction>

    fun findAllByUserIdAndOrganizationId(userId: Long, organizationId: Long): List<UserTransaction>

    // SUM queries for aggregation
    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM balance_transactions WHERE organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationId(organizationId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM balance_transactions WHERE organization_id = :organizationId AND user_id = :userId AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndUserId(organizationId: Long, userId: Long): BigDecimal

    @Query(
        value = "SELECT COALESCE(SUM(amount), 0) FROM balance_transactions WHERE organization_id = :organizationId AND transaction_tag = :tag AND deleted IS NULL",
        nativeQuery = true
    )
    fun sumAmountByOrganizationIdAndTag(organizationId: Long, tag: String): BigDecimal
}