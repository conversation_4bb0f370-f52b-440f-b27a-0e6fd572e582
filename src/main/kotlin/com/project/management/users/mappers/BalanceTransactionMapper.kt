package com.project.management.users.mappers

import com.project.management.common.annotation.NewMappingOrganizationEntity
import com.project.management.users.User
import com.project.management.users.models.UserTransaction
import com.project.management.users.models.UserTransactionTag
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.math.BigDecimal

internal fun balanceTransactionEntity(
    organizationId: Long,
    createdBy: User,
    targetUserId: Long,
    amount: BigDecimal,
    description: String,
    currentAmount: BigDecimal,
    tag: UserTransactionTag,
    reference: String?
): UserTransaction {
    return UserTransaction(
        organizationId = organizationId,
        amount = amount,
        currentAmount = currentAmount,
        description = description,
        userId = targetUserId,
        transactionTag = tag,
        reference = reference,
        createdByDetails = createdBy,
        createdBy = createdBy.id,
        updatedBy = createdBy.id,
        version = 1
    )
}
