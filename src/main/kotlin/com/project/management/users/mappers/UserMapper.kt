package com.project.management.users.mappers

import com.project.management.common.utility.Utility
import com.project.management.users.requests.UserRequestDto
import com.project.management.users.responses.UserResponseDto
import com.project.management.users.User
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.time.ZonedDateTime

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface UserMapper {
    @Mapping(target = "isAdmin", source = "user.admin")
    fun toUserResponseDto(user: User): UserResponseDto
}

internal fun UserRequestDto.toEntity(
    organizationId: Long,
    createdBy: Long?,
    updatedBy: Long?,
    organizationCode: String,
    isAdmin: Boolean
): User {
    return User(
        username = username,
        password = password,
        organizationCode = organizationCode,
        name = name,
        email = "",
        phoneNumber = phoneNumber,
        secondaryPhoneNumber = "",
        nationalId = "",
        isAdmin = isAdmin,
        jobTitle = "",
        balance = 0.0.toBigDecimal(),
        photoUrl = "",
        organizationId = organizationId,
        createdBy = createdBy!!,
        updatedBy = updatedBy!!,
    )
}